{"name": "tempest/framework", "description": "The PHP framework that gets out of your way.", "license": "MIT", "require": {"doctrine/inflector": "^2.0", "egulias/email-validator": "^4.0.2", "ext-dom": "*", "ext-fileinfo": "*", "ext-intl": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-pdo": "*", "ext-readline": "*", "ext-simplexml": "*", "filp/whoops": "^2.15", "giggsey/libphonenumber-for-php-lite": "^9.0", "guzzlehttp/guzzle": "^7.8.2", "laminas/laminas-diactoros": "^3.3", "league/commonmark": "^2.7", "league/flysystem": "^3.29.1", "league/mime-type-detection": "^1.16", "monolog/monolog": "^3.7.0", "nette/php-generator": "^4.1.6", "nikic/php-parser": "^5.3", "php": "^8.4", "psr-discovery/http-client-implementations": "^1.2", "psr-discovery/http-factory-implementations": "^1.4", "psr/cache": "^3.0", "psr/clock": "^1.0.0", "psr/http-client": "^1.0.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0|^2.0", "psr/log": "^3.0.0", "symfony/cache": "^7.3", "symfony/mailer": "^7.2.6", "symfony/process": "^7.1.7", "symfony/uid": "^7.1", "symfony/var-dumper": "^7.1", "symfony/var-exporter": "^7.1", "symfony/yaml": "^7.3", "tempest/highlight": "^2.11.4", "vlucas/phpdotenv": "^5.6.1", "voku/portable-ascii": "^2.0.3"}, "require-dev": {"aws/aws-sdk-php": "^3.338.0", "azure-oss/storage-blob-flysystem": "^1.2", "carthage-software/mago": "0.26.1", "guzzlehttp/psr7": "^2.6.1", "league/flysystem-aws-s3-v3": "^3.25.1", "league/flysystem-ftp": "^3.25.1", "league/flysystem-google-cloud-storage": "^3.25.1", "league/flysystem-local": "^3.25.1", "league/flysystem-memory": "^3.25.1", "league/flysystem-read-only": "^3.25.1", "league/flysystem-sftp-v3": "^3.25.1", "league/flysystem-ziparchive": "^3.25.1", "masterminds/html5": "^2.9", "microsoft/azure-storage-blob": "^1.5", "mikey179/vfsstream": "^2.0@dev", "nesbot/carbon": "^3.8", "nyholm/psr7": "^1.8", "phpat/phpat": "^0.11.0", "phpbench/phpbench": "84.x-dev", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^12.2.3", "predis/predis": "^3.0.0", "rector/rector": "^2.0-rc2", "spatie/phpunit-snapshot-assertions": "^5.1.8", "spaze/phpstan-disallowed-calls": "^4.0", "symfony/amazon-mailer": "^7.2.0", "symfony/postmark-mailer": "^7.2.6", "symplify/monorepo-builder": "^11.2", "tempest/blade": "dev-main", "twig/twig": "^3.16"}, "replace": {"tempest/auth": "self.version", "tempest/cache": "self.version", "tempest/clock": "self.version", "tempest/command-bus": "self.version", "tempest/console": "self.version", "tempest/container": "self.version", "tempest/core": "self.version", "tempest/cryptography": "self.version", "tempest/database": "self.version", "tempest/datetime": "self.version", "tempest/debug": "self.version", "tempest/discovery": "self.version", "tempest/event-bus": "self.version", "tempest/generation": "self.version", "tempest/http": "self.version", "tempest/http-client": "self.version", "tempest/icon": "self.version", "tempest/intl": "self.version", "tempest/kv-store": "self.version", "tempest/log": "self.version", "tempest/mail": "self.version", "tempest/mapper": "self.version", "tempest/reflection": "self.version", "tempest/router": "self.version", "tempest/storage": "self.version", "tempest/support": "self.version", "tempest/validation": "self.version", "tempest/view": "self.version", "tempest/vite": "self.version"}, "suggest": {"ext-pcntl": "Required to use some interactive console components.", "ext-posix": "Required to use some interactive console components."}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"Tempest\\Auth\\": "packages/auth/src", "Tempest\\Cache\\": "packages/cache/src", "Tempest\\Clock\\": "packages/clock/src", "Tempest\\CommandBus\\": "packages/command-bus/src", "Tempest\\Console\\": "packages/console/src", "Tempest\\Container\\": "packages/container/src", "Tempest\\Core\\": "packages/core/src", "Tempest\\Cryptography\\": "packages/cryptography/src", "Tempest\\Database\\": "packages/database/src", "Tempest\\DateTime\\": "packages/datetime/src", "Tempest\\Debug\\": "packages/debug/src", "Tempest\\Discovery\\": "packages/discovery/src", "Tempest\\EventBus\\": "packages/event-bus/src", "Tempest\\Framework\\": "src/Tempest/Framework", "Tempest\\Generation\\": "packages/generation/src", "Tempest\\HttpClient\\": "packages/http-client/src", "Tempest\\Http\\": "packages/http/src", "Tempest\\Icon\\": "packages/icon/src", "Tempest\\Intl\\": "packages/intl/src", "Tempest\\KeyValue\\": "packages/kv-store/src", "Tempest\\Log\\": "packages/log/src", "Tempest\\Mail\\": "packages/mail/src", "Tempest\\Mapper\\": "packages/mapper/src", "Tempest\\Reflection\\": "packages/reflection/src", "Tempest\\Router\\": "packages/router/src", "Tempest\\Storage\\": "packages/storage/src", "Tempest\\Support\\": "packages/support/src", "Tempest\\Validation\\": "packages/validation/src", "Tempest\\View\\": "packages/view/src", "Tempest\\Vite\\": "packages/vite/src"}, "files": ["packages/clock/src/functions.php", "packages/command-bus/src/functions.php", "packages/container/src/functions.php", "packages/core/src/functions.php", "packages/database/src/functions.php", "packages/datetime/src/constants.php", "packages/datetime/src/functions.php", "packages/debug/src/functions.php", "packages/event-bus/src/functions.php", "packages/http/src/functions.php", "packages/icon/src/functions.php", "packages/intl/src/Number/functions.php", "packages/intl/src/functions.php", "packages/mapper/src/functions.php", "packages/reflection/src/functions.php", "packages/router/src/functions.php", "packages/support/src/Arr/functions.php", "packages/support/src/Comparison/functions.php", "packages/support/src/Filesystem/functions.php", "packages/support/src/Html/functions.php", "packages/support/src/Json/functions.php", "packages/support/src/Math/constants.php", "packages/support/src/Math/functions.php", "packages/support/src/Namespace/functions.php", "packages/support/src/Path/functions.php", "packages/support/src/Random/functions.php", "packages/support/src/Regex/functions.php", "packages/support/src/Str/constants.php", "packages/support/src/Str/functions.php", "packages/support/src/functions.php", "packages/view/src/functions.php", "packages/vite/src/functions.php"]}, "autoload-dev": {"psr-4": {"Tempest\\Cache\\Tests\\": "packages/cache/tests", "Tempest\\Clock\\Tests\\": "packages/clock/tests", "Tempest\\CommandBus\\Tests\\": "packages/command-bus/tests", "Tempest\\Console\\Tests\\": "packages/console/tests", "Tempest\\Container\\Tests\\": "packages/container/tests", "Tempest\\Core\\Tests\\": "packages/core/tests", "Tempest\\Cryptography\\Tests\\": "packages/cryptography/tests", "Tempest\\Database\\Tests\\": "packages/database/tests", "Tempest\\DateTime\\Tests\\": "packages/datetime/tests", "Tempest\\EventBus\\Tests\\": "packages/event-bus/tests", "Tempest\\Generation\\Tests\\": "packages/generation/tests", "Tempest\\HttpClient\\Tests\\": "packages/http-client/tests", "Tempest\\Http\\Tests\\": "packages/http/tests", "Tempest\\Icon\\Tests\\": "packages/icon/tests", "Tempest\\Intl\\Tests\\": "packages/intl/tests", "Tempest\\KeyValue\\Tests\\": "packages/kv-store/tests", "Tempest\\Log\\Tests\\": "packages/log/tests", "Tempest\\Mail\\Tests\\": "packages/mail/tests", "Tempest\\Mapper\\Tests\\": "packages/mapper/tests", "Tempest\\Reflection\\Tests\\": "packages/reflection/tests", "Tempest\\Router\\Tests\\": "packages/router/tests", "Tempest\\Storage\\Tests\\": "packages/storage/tests", "Tempest\\Support\\Tests\\": "packages/support/tests", "Tempest\\Validation\\Tests\\": "packages/validation/tests", "Tempest\\View\\Tests\\": "packages/view/tests", "Tempest\\Vite\\Tests\\": "packages/vite/tests", "Tests\\Tempest\\": "tests"}}, "bin": ["packages/console/bin/tempest"], "config": {"allow-plugins": {"carthage-software/mago": true}}, "scripts": {"phpunit": "@php -d memory_limit=2G vendor/bin/phpunit --display-warnings --display-skipped --display-deprecations --display-errors --display-notices", "coverage": "vendor/bin/phpunit --coverage-html build/reports/html --coverage-clover build/reports/clover.xml", "mago:fmt": "vendor/bin/mago fmt && vendor/bin/mago lint --fix --potentially-unsafe --fmt", "mago:lint": "vendor/bin/mago lint --minimum-level=note", "phpstan": "vendor/bin/phpstan analyse src tests --memory-limit=1G", "rector": "vendor/bin/rector process --no-ansi", "merge": "php -d\"error_reporting = E_ALL & ~E_DEPRECATED\" vendor/bin/monorepo-builder merge", "intl:plural": "./packages/intl/bin/plural-rules.php", "release": ["composer qa", "./bin/release"], "qa": ["composer mago:fmt", "composer merge", "./bin/validate-packages", "./tempest discovery:clear --no-interaction", "composer rector", "composer <PERSON><PERSON><PERSON><PERSON>", "composer mago:lint", "composer php<PERSON>"]}}