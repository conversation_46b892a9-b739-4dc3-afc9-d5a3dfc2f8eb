<?xml version="1.0" encoding="UTF-8"?>
<phpunit
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.2/phpunit.xsd"
        colors="true"
        displayDetailsOnTestsThatTriggerDeprecations="true"
        displayDetailsOnTestsThatTriggerErrors="true"
        displayDetailsOnTestsThatTriggerNotices="true"
        displayDetailsOnTestsThatTriggerWarnings="true"
        bootstrap="./tests/bootstrap.php"
        cacheDirectory=".cache/phpunit"
>
  <testsuites>
    <testsuite name="Integration">
      <directory suffix="Test.php">tests/Integration</directory>
    </testsuite>
    <testsuite name="Unit">
      <directory suffix="Test.php">packages/**/tests</directory>
    </testsuite>
  </testsuites>
  <coverage />
  <source>
    <include>
      <directory suffix=".php">src</directory>
    </include>
    <exclude>
      <directory suffix=".php">src/Tempest/**/tests</directory>
      <directory suffix=".cache.php">src</directory>
    </exclude>
  </source>
  <php>
    <env name="ENVIRONMENT" value="testing" />
    <env name="BASE_URI" value="" />
    <env name="INTERNAL_CACHES" value="null" />
    <env name="DISCOVERY_CACHE" value="true" />
  </php>
</phpunit>
