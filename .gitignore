# Top level directories
/log/
/package-lock.json

# Top-level+Sub directories
.tempest/
.cache/
.idea/
build/
sessions/
vendor/
# @TODO(aidan-casey): clean this up
packages/database.sqlite
packages/database/src/database.sqlite
src/Tempest/database.sqlite
tests/Fixtures/database.sqlite
tests/Unit/Console/test-console.log
src/Tempest/Database/src/database.sqlite
.env
composer.lock
debug.log
tempest.log
public/static
tests/Unit/Log
!packages/log
node_modules
dist
profile/
db-main.sqlite
db-tenant-1.sqlite
db-tenant-2.sqlite
