{"useTabs": true, "typescript": {"semiColons": "asi", "quoteStyle": "preferSingle"}, "markup": {"printWidth": 100, "preferAttrsSingleLine": true, "vBindStyle": "short", "vOnStyle": "short", "vForDelimiterStyle": "in", "vSlotStyle": "vSlot", "vBindSameNameShortHand": true, "component.selfClosing": true, "html.normal.selfClosing": true, "html.void.selfClosing": true, "svg.selfClosing": true, "formatComments": true}, "exec": {"cwd": "${configDir}", "commands": [{"command": "./vendor/bin/mago fmt --stdin-input", "exts": ["php"]}]}, "excludes": ["**/.tempest", "**/composer.json", "**/node_modules", "**/*-lock.json", "**/vendor", "tests/**/*.{html,twig,json,yml,yaml}", "CHANGELOG.md"], "plugins": ["https://plugins.dprint.dev/typescript-0.95.7.wasm", "https://plugins.dprint.dev/json-0.20.0.wasm", "https://plugins.dprint.dev/markdown-0.18.0.wasm", "https://plugins.dprint.dev/toml-0.7.0.wasm", "https://plugins.dprint.dev/g-plane/malva-v0.12.1.wasm", "https://plugins.dprint.dev/g-plane/markup_fmt-v0.22.0.wasm", "https://plugins.dprint.dev/g-plane/pretty_yaml-v0.5.1.wasm", "https://plugins.dprint.dev/exec-0.5.1.json@492414e39dea4dccc07b4af796d2f4efdb89e84bae2bd4e1e924c0cc050855bf"]}