<?php

declare(strict_types=1);

namespace Tempest\Validation\Tests\Rules;

use PHPUnit\Framework\Attributes\Test;
use Tempest\Database\Migrations\CreateMigrationsTable;
use Tempest\Validation\Rules\Exists;
use Tests\Tempest\Fixtures\Migrations\CreateBookTable;
use Tests\Tempest\Fixtures\Modules\Books\Models\Book;
use Tests\Tempest\Integration\FrameworkIntegrationTestCase;

final class ExistsTest extends FrameworkIntegrationTestCase
{
    #[Test]
    public function can_validate_existing_model(): void
    {
        $this->migrate(
            CreateMigrationsTable::class,
            CreateBookTable::class
        );

        $book = Book::create(title: 'The Hobbit');

        $rule = new Exists(Book::class);
        $this->assertTrue($rule->isValid($book->id->id));
    }
}
