<?php

declare(strict_types=1);

namespace Tempest\Validation\Tests\Rules;

use InvalidArgumentException;
use PHPUnit\Framework\Attributes\Test;
use Tempest\Database\Migrations\CreateMigrationsTable;
use Tempest\Validation\Rules\Exists;
use Tests\Tempest\Fixtures\Migrations\CreateAuthorTable;
use Tests\Tempest\Fixtures\Migrations\CreateBookTable;
use Tests\Tempest\Fixtures\Modules\Books\Models\Book;
use Tests\Tempest\Integration\FrameworkIntegrationTestCase;

/**
 * @internal
 */
final class ExistsTest extends FrameworkIntegrationTestCase
{
    #[Test]
    public function can_validate_existing_model(): void
    {
        $this->migrate(
            CreateMigrationsTable::class,
            CreateAuthorTable::class,
            CreateBookTable::class
        );

        $book = Book::create(title: 'The Hobbit');

        $rule = new Exists(Book::class);
        $this->assertTrue($rule->isValid($book->id->id));
    }

    #[Test]
    public function throws_exception_for_invalid_model_class(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Model NonExistentModel does not exist');

        new Exists('NonExistentModel');
    }

    #[Test]
    public function returns_false_for_null_value(): void
    {
        $rule = new Exists(Book::class);

        $this->assertFalse($rule->isValid(null));
    }

    #[Test]
    public function returns_false_for_non_existent_record(): void
    {
        $this->migrate(
            CreateMigrationsTable::class,
            CreateBookTable::class
        );

        $rule = new Exists(Book::class);

        // Test with a non-existent ID
        $this->assertFalse($rule->isValid(99999));
        $this->assertFalse($rule->isValid('non-existent-id'));
    }

    #[Test]
    public function returns_correct_error_message(): void
    {
        $rule = new Exists(Book::class);

        $expectedMessage = sprintf('Record for model %s does not exist', Book::class);
        $this->assertSame($expectedMessage, $rule->message());
    }

    #[Test]
    public function can_not_validate_with_string_id(): void
    {
        $this->migrate(
            CreateMigrationsTable::class,
            CreateBookTable::class
        );

        $book = Book::create(title: 'The Lord of the Rings');

        $rule = new Exists(Book::class);

        // Test with string representation of ID
        $this->assertFalse($rule->isValid((string) $book->id->id));
    }

    #[Test]
    public function can_validate_with_integer_id(): void
    {
        $this->migrate(
            CreateMigrationsTable::class,
            CreateBookTable::class
        );

        $book = Book::create(title: 'The Silmarillion');

        $rule = new Exists(Book::class);

        // Test with integer ID
        $this->assertTrue($rule->isValid($book->id->id));
    }
}
