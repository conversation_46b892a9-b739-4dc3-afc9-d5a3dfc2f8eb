<?php

declare(strict_types=1);

namespace Tempest\Validation\Tests\Rules;

use PHPUnit\Framework\Attributes\Test;
use Tempest\Database\Migrations\CreateMigrationsTable;
use Tempest\Validation\Rules\Exists;
use Tests\Tempest\Fixtures\Migrations\CreateAuthorTable;
use Tests\Tempest\Fixtures\Migrations\CreateBookTable;
use Tests\Tempest\Fixtures\Modules\Books\Models\Author;
use Tests\Tempest\Fixtures\Modules\Books\Models\Book;
use Tests\Tempest\Integration\FrameworkIntegrationTestCase;

/**
 * Integration tests for the Exists validation rule.
 * 
 * These tests focus on actual database interactions to verify that the Exists rule
 * correctly validates record existence with real database operations.
 * 
 * @internal
 */
final class ExistsIntegrationTest extends FrameworkIntegrationTestCase
{
    #[Test]
    public function validates_existing_record_returns_true(): void
    {
        $this->migrate(
            CreateMigrationsTable::class,
            CreateBookTable::class
        );

        // Create a book record in the database
        $book = Book::create(title: 'The Hobbit');

        $rule = new Exists(Book::class);
        
        // Test that the rule correctly identifies the existing record
        $this->assertTrue($rule->isValid($book->id->id));
    }

    #[Test]
    public function validates_non_existent_record_returns_false(): void
    {
        $this->migrate(
            CreateMigrationsTable::class,
            CreateBookTable::class
        );

        $rule = new Exists(Book::class);
        
        // Test with a non-existent ID
        $this->assertFalse($rule->isValid(99999));
        $this->assertFalse($rule->isValid(12345));
    }

    #[Test]
    public function validates_multiple_existing_records(): void
    {
        $this->migrate(
            CreateMigrationsTable::class,
            CreateBookTable::class
        );

        // Create multiple book records
        $book1 = Book::create(title: 'The Lord of the Rings');
        $book2 = Book::create(title: 'The Silmarillion');
        $book3 = Book::create(title: 'Unfinished Tales');

        $rule = new Exists(Book::class);
        
        // Test that all existing records are validated correctly
        $this->assertTrue($rule->isValid($book1->id->id));
        $this->assertTrue($rule->isValid($book2->id->id));
        $this->assertTrue($rule->isValid($book3->id->id));
        
        // Test that non-existent IDs still return false
        $this->assertFalse($rule->isValid(99999));
    }

    #[Test]
    public function validates_different_model_types(): void
    {
        $this->migrate(
            CreateMigrationsTable::class,
            CreateAuthorTable::class,
            CreateBookTable::class
        );

        // Create records for different models
        $author = Author::create(name: 'J.R.R. Tolkien');
        $book = Book::create(title: 'The Hobbit');

        $authorRule = new Exists(Author::class);
        $bookRule = new Exists(Book::class);

        // Test that each rule validates its respective model correctly
        $this->assertTrue($authorRule->isValid($author->id->id));
        $this->assertTrue($bookRule->isValid($book->id->id));

        // Test with IDs that definitely don't exist in either table
        $this->assertFalse($authorRule->isValid(99999));
        $this->assertFalse($bookRule->isValid(99999));

        // Create additional records to ensure we have different IDs
        $author2 = Author::create(name: 'C.S. Lewis');
        $book2 = Book::create(title: 'The Chronicles of Narnia');

        // Verify the new records exist in their respective tables
        $this->assertTrue($authorRule->isValid($author2->id->id));
        $this->assertTrue($bookRule->isValid($book2->id->id));
    }

    #[Test]
    public function validates_edge_cases_with_large_id_numbers(): void
    {
        $this->migrate(
            CreateMigrationsTable::class,
            CreateBookTable::class
        );

        $rule = new Exists(Book::class);
        
        // Test with very large ID numbers that don't exist
        $this->assertFalse($rule->isValid(PHP_INT_MAX));
        $this->assertFalse($rule->isValid(999999999));
        $this->assertFalse($rule->isValid(2147483647)); // Max 32-bit integer
    }

    #[Test]
    public function validates_after_record_deletion(): void
    {
        $this->migrate(
            CreateMigrationsTable::class,
            CreateBookTable::class
        );

        // Create a book record
        $book = Book::create(title: 'Temporary Book');
        $bookId = $book->id->id;

        $rule = new Exists(Book::class);
        
        // Verify the record exists
        $this->assertTrue($rule->isValid($bookId));
        
        // Delete the record
        $book->delete();
        
        // Verify the rule now returns false for the deleted record
        $this->assertFalse($rule->isValid($bookId));
    }

    #[Test]
    public function validates_with_sequential_id_creation(): void
    {
        $this->migrate(
            CreateMigrationsTable::class,
            CreateBookTable::class
        );

        $rule = new Exists(Book::class);
        $createdIds = [];
        
        // Create multiple records and track their IDs
        for ($i = 1; $i <= 5; $i++) {
            $book = Book::create(title: "Book {$i}");
            $createdIds[] = $book->id->id;
            
            // Verify each created record exists
            $this->assertTrue($rule->isValid($book->id->id));
        }
        
        // Verify all created IDs are still valid
        foreach ($createdIds as $id) {
            $this->assertTrue($rule->isValid($id));
        }
        
        // Verify IDs that were never created don't exist
        $maxId = max($createdIds);
        $this->assertFalse($rule->isValid($maxId + 1));
        $this->assertFalse($rule->isValid($maxId + 100));
    }
}
