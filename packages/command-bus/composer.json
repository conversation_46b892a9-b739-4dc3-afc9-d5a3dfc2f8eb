{"name": "tempest/command-bus", "description": "A command bus component designed to dispatch commands to their respective handlers.", "require": {"php": "^8.4", "tempest/core": "dev-main", "tempest/console": "dev-main", "tempest/container": "dev-main"}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Tempest\\CommandBus\\": "src"}}, "autoload-dev": {"psr-4": {"Tempest\\CommandBus\\Tests\\": "tests"}}, "license": "MIT", "minimum-stability": "dev"}