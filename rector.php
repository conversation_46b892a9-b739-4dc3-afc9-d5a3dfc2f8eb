<?php

declare(strict_types=1);

use <PERSON>\Arguments\Rector\ClassMethod\ArgumentAdderRector;
use <PERSON>\Caching\ValueObject\Storage\FileCacheStorage;
use Rector\CodingStyle\Rector\Encapsed\EncapsedStringsToSprintfRector;
use <PERSON>\CodingStyle\Rector\Stmt\NewlineAfterStatementRector;
use <PERSON>\Config\RectorConfig;
use Rector\DeadCode\Rector\PropertyProperty\RemoveNullPropertyInitializationRector;
use Rector\Php70\Rector\StaticCall\StaticCallOnNonStaticToInstanceCallRector;
use Rector\Php74\Rector\Closure\ClosureToArrowFunctionRector;
use Rector\Php74\Rector\Property\RestoreDefaultNullToNullableTypePropertyRector;
use Rector\Php74\Rector\Ternary\ParenthesizeNestedTernaryRector;
use Rector\Php81\Rector\Array_\FirstClassCallableRector;
use Rector\Php81\Rector\FuncCall\NullToStrictStringFuncCallArgRector;
use <PERSON>\Php81\Rector\Property\ReadOnlyPropertyRector;
use <PERSON>\Php82\Rector\Class_\ReadOnlyClassRector;
use Rector\Php82\Rector\Param\AddSensitiveParameterAttributeRector;
use Rector\Php83\Rector\ClassMethod\AddOverrideAttributeToOverriddenMethodsRector;
use Rector\Php84\Rector\Param\ExplicitNullableParamTypeRector;
use Rector\TypeDeclaration\Rector\ArrowFunction\AddArrowFunctionReturnTypeRector;
use Rector\TypeDeclaration\Rector\ClassMethod\ReturnNeverTypeRector;
use Rector\TypeDeclaration\Rector\Closure\ClosureReturnTypeRector;
use Rector\TypeDeclaration\Rector\Empty_\EmptyOnNullableObjectToInstanceOfRector;

return RectorConfig::configure()
    ->withCache('./.cache/rector', FileCacheStorage::class)
    ->withPaths([
        __DIR__ . '/src',
        __DIR__ . '/tests',
    ])
    ->withConfiguredRule(AddSensitiveParameterAttributeRector::class, [
        'sensitive_parameters' => [
            'password',
            'secret',
        ],
    ])
    ->withRules([
        ExplicitNullableParamTypeRector::class,
    ])
    ->withSkip([
        AddOverrideAttributeToOverriddenMethodsRector::class,
        ArgumentAdderRector::class,
        ClosureToArrowFunctionRector::class,
        EmptyOnNullableObjectToInstanceOfRector::class,
        FirstClassCallableRector::class,
        NullToStrictStringFuncCallArgRector::class,
        ReadOnlyClassRector::class,
        ReadOnlyPropertyRector::class,
        RemoveNullPropertyInitializationRector::class,
        AddSensitiveParameterAttributeRector::class,
        RestoreDefaultNullToNullableTypePropertyRector::class,
        ReturnNeverTypeRector::class,
        StaticCallOnNonStaticToInstanceCallRector::class,
        ClosureReturnTypeRector::class,
        EncapsedStringsToSprintfRector::class,
        AddArrowFunctionReturnTypeRector::class,
    ])
    ->withParallel(300, 10, 10)
    ->withPreparedSets(
        codeQuality: false,
        codingStyle: true,
        privatization: true,
        naming: false,
        earlyReturn: true,
    )
    ->withDeadCodeLevel(40)
    ->withMemoryLimit('3G')
    ->withPhpSets(php83: true)
    ->withTypeCoverageLevel(37);
